/** @type {import('next').NextConfig} */

// Suppress punycode deprecation warning
const originalEmit = process.emit;
process.emit = function (name, data, ...args) {
  if (
    name === 'warning' &&
    typeof data === 'object' &&
    data.name === 'DeprecationWarning' &&
    data.message &&
    data.message.includes('punycode')
  ) {
    return false;
  }
  return originalEmit.apply(process, arguments);
};

// Also suppress console warnings
const originalWarn = console.warn;
console.warn = function (...args) {
  const message = args.join(' ');
  if (message.includes('punycode') && message.includes('deprecated')) {
    return;
  }
  return originalWarn.apply(console, args);
};

const nextConfig = {
  experimental: {
    serverComponentsExternalPackages: ['@prisma/client', 'bcryptjs'],
  },
  webpack: (config, { isServer }) => {
    // Handle d3 modules properly
    if (!isServer) {
      config.resolve.fallback = {
        ...config.resolve.fallback,
        fs: false,
        net: false,
        tls: false,
      };
    }

    // Fix punycode deprecation warning by resolving to userland alternative
    config.resolve.alias = {
      ...config.resolve.alias,
      punycode: require.resolve('punycode.js'),
    };

    return config;
  },
  env: {
    NEXTAUTH_URL: process.env.NEXTAUTH_URL,
    NEXTAUTH_SECRET: process.env.NEXTAUTH_SECRET,
    SUPABASE_URL: process.env.NEXT_PUBLIC_SUPABASE_URL,
    SUPABASE_ANON_KEY: process.env.NEXT_PUBLIC_SUPABASE_ANON_KEY,
    SUPABASE_SERVICE_ROLE_KEY: process.env.SUPABASE_SERVICE_ROLE_KEY,
    DATABASE_URL: process.env.DATABASE_URL,
  },
  images: {
    domains: ['localhost'],
  },
};

module.exports = nextConfig;
